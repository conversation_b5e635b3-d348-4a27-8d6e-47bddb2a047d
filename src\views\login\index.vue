<template>
  <div class="login-container flx-center">
    <div class="login-box">
      <div class="login-actions">
        <SwitchDark class="dark-switch" />
        <SwitchLanguage class="language-switch" />
      </div>
      <div class="login-left">
        <img class="login-left-img" src="@/assets/images/login_left.png" alt="login" />
      </div>
      <div class="login-form">
        <div class="login-logo">
          <img class="login-icon" src="@/assets/images/logo.png" alt="" />
          <h2 class="logo-text">
            <div ref="chart" class="chart-container"></div>
          </h2>
        </div>
        <LoginForm />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="login">
import LoginForm from "./components/LoginForm.vue";
import SwitchDark from "@/components/SwitchDark/index.vue";
import SwitchLanguage from "@/components/SwitchLanguage/index.vue";
import * as echarts from "echarts";
import { onMounted, ref } from "vue";

const chart = ref<HTMLDivElement | null>(null);

onMounted(() => {
  if (chart.value) {
    const myChart = echarts.init(chart.value);
    const option = {
      graphic: {
        elements: [
          {
            type: "text",
            left: "center",
            top: "center",
            style: {
              text: "HicloudIot",
              fontSize: 57,
              fontWeight: "bold",
              lineDash: [0, 200],
              lineDashOffset: 0,
              fill: "transparent",
              stroke: "#6DA9E8",
              lineWidth: 1
            },
            keyframeAnimation: {
              duration: 3000,
              loop: true,
              keyframes: [
                {
                  percent: 0.7,
                  style: {
                    fill: "transparent",
                    lineDashOffset: 200,
                    lineDash: [200, 0]
                  }
                },
                {
                  // Stop for a while.
                  percent: 0.9,
                  style: {
                    fill: "transparent"
                  }
                },
                {
                  percent: 0.9,
                  style: {
                    fill: "#387DDE"
                  }
                },
                {
                  percent: 1, // 添加结束帧
                  style: {
                    fill: "#387DDE" // 根据需求设置最终状态
                  }
                }
              ]
            }
          }
        ]
      }
    };
    myChart.setOption(option);
  }
});
</script>

<style scoped lang="scss">
@import "./index";
.chart-container {
  width: 300px;
  height: 120px; // 根据需要调整高度
}
</style>
