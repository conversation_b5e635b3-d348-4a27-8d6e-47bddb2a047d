<template>
  <div class="message">
    <el-popover placement="bottom" :width="360" trigger="click">
      <template #reference>
        <el-badge v-if="unreadMessages?.length > 0" :value="unreadMessages?.length || 0" class="item">
          <i :class="'iconfont icon-xiaoxi'" class="toolBar-icon"></i>
        </el-badge>
        <i v-else :class="'iconfont icon-xiaoxi'" class="toolBar-icon"></i>
      </template>
      <el-tabs v-model="activeName">
        <el-tab-pane :label="`${t('common.message')} (${unreadMessages?.length || 0})`" name="first">
          <div class="message-container">
            <el-scrollbar height="300px" v-if="unreadMessages?.length > 0">
              <div class="message-list">
                <div class="message-item" v-for="item in unreadMessages" :key="item.id" @click="navigateToLogsMessages">
                  <img src="@/assets/images/msg01.png" alt="" class="message-icon" />
                  <div class="message-content">
                    <span class="message-title">{{ isChinese ? item.title : item.titleEn || item.title }}</span>
                    <div class="message-text">{{ isChinese ? item.message : item.messageEn || item.message }}</div>
                    <span class="message-date">{{ item.creationTime }}</span>
                  </div>
                </div>
              </div>
              <div class="view-all" @click="navigateToLogsMessages">
                <span>{{ $t("common.viewAll") }}</span>
                <el-icon><ArrowRight /></el-icon>
              </div>
            </el-scrollbar>
            <div class="message-empty" v-else>
              <img src="@/assets/images/notData.png" alt="notData" />
              <div>{{ $t("common.noMessage") }}</div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-popover>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import { onMounted, ref, computed } from "vue";
import { getUnreadMessages } from "@/api/modules/message";
import { useI18n } from "vue-i18n";
import { useGlobalStore } from "@/stores/modules/global";
import { ArrowRight } from "@element-plus/icons-vue";
const { t } = useI18n();
const router = useRouter();

const globalStore = useGlobalStore();
const isChinese = computed(() => globalStore.language === "zh");

interface Message {
  id: string;
  title: string;
  titleEn: string;
  message: string;
  messageEn: string;
  deviceType: string;
  markRead: number;
  creationTime: string;
}

const activeName = ref("first");
let unreadMessages = ref<Message[]>();

const getUnreadMessagesData = async () => {
  try {
    const resultData = await getUnreadMessages();
    if (resultData && resultData.code === "200") {
      // 确保 resultData.data 是一个数组，并且每个元素都符合 Message 接口
      if (Array.isArray(resultData.data)) {
        unreadMessages.value = resultData.data;
      } else {
        console.error("Invalid data format:", resultData.data);
      }
    }
  } catch (error) {
    console.error("Failed to fetch unread messages:", error);
  }
};

const navigateToLogsMessages = () => {
  router.push("/logs/messages"); // 跳转到 /logs/messages
};

onMounted(() => {
  getUnreadMessagesData();
});
</script>

<style scoped lang="scss">
.message-container {
  position: relative;
}

.message-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 260px;
  line-height: 45px;
  img {
    width: 80px;
    margin-bottom: 10px;
  }
}

.message-list {
  display: flex;
  flex-direction: column;
  .message-item {
    display: flex;
    align-items: flex-start;
    padding: 15px;
    border-bottom: 1px solid var(--el-border-color-light);
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      background-color: var(--el-fill-color-light);
    }

    &:last-child {
      border-bottom: none;
    }

    .message-icon {
      width: 40px;
      height: 40px;
      margin: 0 15px 0 0;
      border-radius: 8px;
    }

    .message-content {
      display: flex;
      flex-direction: column;
      flex: 1;
      overflow: hidden;

      .message-title {
        margin-bottom: 5px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .message-text {
        margin-bottom: 5px;
        font-size: 13px;
        color: var(--el-text-color-regular);
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.5;
      }

      .message-date {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
  }
}

.view-all {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
  font-size: 13px;
  font-weight: 500;
  color: var(--el-color-primary);
  background-color: var(--el-fill-color-lighter);
  cursor: pointer;
  transition: all 0.3s ease;
  border-top: 1px solid var(--el-border-color-light);

  &:hover {
    background-color: var(--el-fill-color-light);
    color: var(--el-color-primary-dark-2);
  }

  .el-icon {
    margin-left: 5px;
    font-size: 12px;
    transition: transform 0.3s ease;
  }

  &:hover .el-icon {
    transform: translateX(3px);
  }
}
</style>
