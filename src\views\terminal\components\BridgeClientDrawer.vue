<template>
  <el-drawer
    v-model="drawerVisible"
    :destroy-on-close="true"
    size="550px"
    :title="`${drawerProps.title} ${t('device.bridgeClient')}`"
    @open="handleOpen"
    @close="handleClose"
  >
    <div v-for="(child, index) in data.children" :key="index">
      <span>{{ child.name }}</span>
      <img :src="child.symbol" alt="" />
    </div>
    <el-dialog v-model="bridgeClientDrawerVisible" :title="$t('device.bridgeClient')" width="600px" draggable>
      <!--      {{ clickNodeProps.row }}-->
      <el-form
        ref="ruleFormRef"
        label-width="180px"
        label-suffix=" :"
        :rules="rules"
        :model="clickNodeProps.row"
        :hide-required-asterisk="clickNodeProps.isView"
      >
        <el-form-item :label="$t('device.name')" prop="name">
          <el-text v-if="!editName">{{ clickNodeProps.row!.name }}</el-text>
          <el-input
            v-model="clickNodeProps.row!.name"
            v-if="editName"
            clearable
            style="width: 150px"
            @input="deviceNameChanged = true"
          ></el-input>
          <el-link target="_blank" :icon="Edit" @click="editDeviceName(clickNodeProps.row)" v-if="!editName">
            {{ $t("common.edit") }}
          </el-link>
          <el-link
            type="primary"
            target="_blank"
            :icon="Check"
            @click="saveDeviceName(clickNodeProps.row, t)"
            v-if="editName"
            :disabled="deviceNameChanged === false"
          >
            {{ $t("common.save") }}
          </el-link>
        </el-form-item>
        <el-form-item :label="$t('device.deviceType')" prop="type">
          <span v-text="clickNodeProps.row?.extra?.type === 1 ? t('device.bridgeClient') : t('terminal.origin.unknow')"></span>
        </el-form-item>
        <el-form-item :label="$t('device.model')" prop="model">
          <el-text>{{ clickNodeProps.row?.extra?.model ? clickNodeProps.row!.extra.model : "--" }}</el-text>
        </el-form-item>
        <el-form-item :label="$t('device.status')" prop="online" v-if="clickNodeProps.isView">
          <el-text :style="{ color: statusTagType === 'danger' ? 'red' : 'green' }">
            {{ statusLabel }}
          </el-text>
        </el-form-item>
        <el-form-item :label="$t('common.version')" prop="deviceModel">
          <el-text>{{ clickNodeProps.row?.extra?.version ? clickNodeProps.row!.extra.version : "--" }}</el-text>
        </el-form-item>
        <el-form-item :label="$t('device.mac')">
          <el-text>{{ clickNodeProps.row?.extra?.macaddr }}</el-text>
        </el-form-item>
        <el-form-item :label="$t('terminal.upwardCount')" prop="blackList">
          <el-text>
            {{ clickNodeProps.row?.extra?.txByte ? (clickNodeProps.row!.extra.txByte / (1024 * 1024)).toFixed(2) : 0 }}MB
          </el-text>
        </el-form-item>
        <el-form-item :label="$t('terminal.downwardCount')" prop="type">
          <el-text>
            {{ clickNodeProps.row?.extra?.rxByte ? (clickNodeProps.row!.extra.rxByte / (1024 * 1024)).toFixed(2) : 0 }}MB
          </el-text>
        </el-form-item>
        <el-form-item :label="$t('common.downNegotiationRate')" prop="internet">
          <el-text>{{ clickNodeProps.row?.extra?.txRate ? clickNodeProps.row!.extra.txRate : 0 }} Mbps</el-text>
        </el-form-item>
        <el-form-item :label="$t('common.upNegotiationRate')" prop="mac">
          <el-text>{{ clickNodeProps.row?.extra?.rxRate ? clickNodeProps.row!.extra.rxRate : 0 }} Mbps</el-text>
        </el-form-item>
        <el-form-item :label="$t('common.latency')" prop="ip">
          <el-text>{{ clickNodeProps.row?.extra?.latency ? clickNodeProps.row!.extra.latency : "--" }} ms</el-text>
        </el-form-item>
        <el-form-item :label="$t('device.connectionDuration')">
          <el-text> {{ clickNodeProps.row?.extra?.time ? formatBootTime(clickNodeProps.row!.extra.time) : "--" }} </el-text>
        </el-form-item>
        <el-form-item :label="$t('device.rssiTip')">
          <el-text> {{ clickNodeProps.row?.extra?.rssi ? clickNodeProps.row!.extra.rssi : "--" }} dBm</el-text>
        </el-form-item>
        <el-form-item style="text-align: center">
          <el-button
            type="primary"
            @click="deleteBridgeClient(clickNodeProps.row, t)"
            :disabled="clickNodeProps.row?.extra?.online == 1"
            >{{ t("common.delete") }}
          </el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <div class="card content-box">
      <ECharts :option="option" ref="chartRef" style="width: 100%; height: 400px" @click="handleNodeClick" />
    </div>
    <template #footer>
      <el-button @click="drawerVisible = false">{{ $t("common.cancel") }}</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" @click="handleSubmit">{{ $t("common.confirm") }}</el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts" name="BridgeClientDrawer">
import { ref, reactive, computed, onMounted, nextTick, watch, toRaw, onBeforeUnmount } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { useI18n } from "vue-i18n";
import { deviceStatusEnum, getDeviceConfigJwe } from "@/api/modules/project";
import ECharts from "@/components/ECharts/index.vue";
import { ECOption } from "@/components/ECharts/config";
import { Check, Edit } from "@element-plus/icons-vue";
import { useGlobalStore } from "@/stores/modules/global";
import {
  deviceNameChanged,
  editName,
  bridgeClientDrawerVisible,
  editDeviceName,
  formatBootTime,
  saveDeviceName,
  drawerVisible,
  clickNodeProps,
  drawerProps,
  generateData,
  data,
  deleteBridgeClient,
  DrawerProps
} from "@/api/interface/bridgeClientDrawer";
import { useUserStore } from "@/stores/modules/user";
import * as echarts from "echarts";
const { t } = useI18n();
const globalStore = useGlobalStore();

const rules = reactive({
  deviceId: [{ required: true, message: t("device.namePlaceholder") }],
  deviceModel: [{ required: false, message: t("device.modelPlaceholder") }],
  deviceType: [{ required: false, message: t("device.typePlaceholder") }],
  mac: [{ required: false, message: t("device.macPlaceholder") }],
  ip: [{ required: false, message: t("device.ipPlaceholder") }]
});

const ruleFormRef = ref<FormInstance>();
const handleSubmit = () => {
  ruleFormRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      const response = await drawerProps.value.api!(drawerProps.value.row);
      console.log("API 响应数据:", response);
      if (!response || response.code !== "200") {
        ElMessage.error({ message: response.msg });
        return;
      }
      ElMessage.success({ message: `${drawerProps.value.title}设备成功！` });
      drawerProps.value.getTableList!();
      drawerVisible.value = false;
    } catch (error) {
      console.log(error);
    }
  });
};

const status = computed(() => drawerProps.value.row.extra?.online);

const statusLabel = computed(() => {
  const statusItem = deviceStatusEnum.value?.find(item => item.value !== status.value);
  return statusItem ? statusItem.label : "";
});

const statusTagType = computed(() => {
  const statusItem = deviceStatusEnum.value?.find(item => item.value !== status.value);
  return statusItem ? statusItem.tagType : "";
});

const handleClose = () => {
  drawerVisible.value = false;
  editName.value = false;
  deviceNameChanged.value = false;
  resetClickNodeProps();

  const chartInstance = chartRef.value?.echartsInstance;
  if (chartInstance) {
    chartInstance.dispose(); // 销毁实例
  }
};

function resetClickNodeProps() {
  clickNodeProps.value = {
    isView: false,
    title: "",
    row: {}
  };
}

const option: ECOption = {
  tooltip: {
    trigger: "item",
    triggerOn: "mousemove"
  },
  // 显示设置网格为全透明，不显示坐标轴
  grid: {
    show: false
  },
  // 完全隐藏X轴
  xAxis: {
    show: false,
    type: "value",
    axisLine: {
      show: false
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      show: false
    },
    splitLine: {
      show: false
    }
  },
  // 完全隐藏Y轴
  yAxis: {
    show: false,
    type: "value",
    axisLine: {
      show: false
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      show: false
    },
    splitLine: {
      show: false
    }
  },
  // 明确设置背景色，暗黑模式下使用半透明深色，明亮模式下使用透明
  backgroundColor: globalStore.isDark ? "rgba(30, 32, 40, 0.8)" : "transparent",
  series: [
    {
      type: "tree",
      data: [data],
      left: "2%",
      right: "2%",
      top: "8%",
      bottom: "20%",
      edgeShape: "polyline",
      symbolSize: [40, 40],
      orient: "vertical",
      expandAndCollapse: true,
      label: {
        position: "top",
        rotate: -90,
        verticalAlign: "middle",
        align: "right",
        fontSize: 9
      },
      leaves: {
        label: {
          position: "bottom",
          rotate: -90,
          verticalAlign: "middle",
          align: "left"
        }
      },
      animationDurationUpdate: 750
    }
  ]
};

const chartRef = ref(null);
const dataLoaded = ref(false);

const handleOpen = async () => {
  try {
    dataLoaded.value = false;

    // 获取用户和设备信息
    const userId = useUserStore().userInfo.userId;
    const deviceId = drawerProps.value.row.deviceId;

    // 发起并行请求获取数据
    const paramsCmd10 = { cmd: 10, deviceId, userId, data: { network: ["brClient"] } };
    const paramsCmd4 = { cmd: 4, deviceId, userId, data: { network: ["brClient"] } };
    const [responseCmd10, responseCmd4] = await Promise.all([getDeviceConfigJwe(paramsCmd10), getDeviceConfigJwe(paramsCmd4)]);

    // 合并返回的数据
    const dataCmd10 = responseCmd10?.data?.network?.brClient || [];
    const dataCmd4 = responseCmd4?.data?.network?.brClient || [];
    const mergedData = mergeBrClientData(dataCmd10, dataCmd4);

    // 将合并数据更新到行数据
    drawerProps.value.row.brClient = mergedData;

    // 更新图表数据
    await generateData(drawerProps.value.row, t);
    data.children = [...data.children]; // 触发响应式更新

    // 等待 DOM 和数据更新完成后初始化图表
    await nextTick(() => {
      initChart();
    });

    dataLoaded.value = true;
  } catch (error) {
    console.error("Error during handleOpen:", error);
  }
};

// 合并 brClient 数据的方法
function mergeBrClientData(dataCmd10, dataCmd4) {
  const mergedData = dataCmd10.map(item10 => {
    const matchingItem = dataCmd4.find(item4 => item4.macaddr === item10.macaddr);
    return matchingItem ? { ...item10, ...matchingItem } : item10;
  });

  dataCmd4.forEach(item4 => {
    if (!mergedData.find(item => item.macaddr === item4.macaddr)) {
      mergedData.push(item4);
    }
  });

  return mergedData;
}

// 初始化图表
function initChart() {
  const chartInstance = chartRef.value?.echartsInstance;

  if (chartInstance) {
    chartInstance.dispose(); // 销毁旧实例
  }

  const newInstance = echarts.init(chartRef.value?.$el);
  newInstance.setOption({
    ...option,
    // 确保坐标轴不显示
    grid: { show: false },
    xAxis: { show: false },
    yAxis: { show: false },
    backgroundColor: globalStore.isDark ? "rgba(30, 32, 40, 0.8)" : "transparent",
    series: [
      {
        ...option.series[0],
        data: [toRaw(data)]
      }
    ]
  });

  setTimeout(() => {
    newInstance.resize(); // 确保图表适配
  }, 100);
}

// 监听 data.children 数据变化并更新图表
watch(
  () => data.children,
  newData => {
    const chartInstance = chartRef.value?.echartsInstance;
    if (chartInstance) {
      chartInstance.setOption({
        // 确保坐标轴不显示
        grid: { show: false },
        xAxis: { show: false },
        yAxis: { show: false },
        backgroundColor: globalStore.isDark ? "rgba(30, 32, 40, 0.8)" : "transparent",
        series: [
          {
            data: [toRaw(newData)]
          }
        ]
      });
    }
  }
);

nextTick(() => {
  const chart = chartRef.value?.echartsInstance;
  if (chart) {
    chart.setOption(option);
    chart.resize();
  }
});

const handleNodeClick = (event: any) => {
  const clickedNode = event.data;
  if (clickedNode && (!clickedNode.children || clickedNode.children.length === 0)) {
    clickNodeProps.value.row = clickedNode;
    bridgeClientDrawerVisible.value = true;
  }
};

onMounted(() => {
  if (chartRef.value) {
    chartRef.value.echartsInstance = echarts.init(chartRef.value.$el);
    // 设置初始配置，确保坐标轴不显示
    chartRef.value.echartsInstance.setOption({
      ...option,
      grid: { show: false },
      xAxis: { show: false },
      yAxis: { show: false },
      backgroundColor: globalStore.isDark ? "rgba(30, 32, 40, 0.8)" : "transparent"
    });
  }
});

// 监听暗黑模式变化，更新图表背景色
watch(
  () => globalStore.isDark,
  newVal => {
    const chartInstance = chartRef.value?.echartsInstance;
    if (chartInstance) {
      chartInstance.setOption({
        backgroundColor: newVal ? "rgba(30, 32, 40, 0.8)" : "transparent"
      });
    }
  }
);

onBeforeUnmount(() => {
  const chartInstance = chartRef.value?.echartsInstance;
  if (chartInstance) {
    chartInstance.dispose(); // 销毁图表实例
  }
});

// 接收父组件传过来的参数
const acceptParams = async (params: DrawerProps) => {
  drawerProps.value = params;
  drawerVisible.value = true;
  await generateData(params.row, t);
};

defineExpose({
  acceptParams
});
</script>

<style scoped>
.card.content-box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* 隐藏图表坐标轴 */
:deep(.echarts-container),
:deep(#echarts),
:deep(.echarts) {
  /* 隐藏所有坐标轴相关元素 */
  .x-axis,
  .y-axis,
  .axis-line,
  .axis-tick,
  .axis-label,
  .split-line {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
  }
}
</style>
