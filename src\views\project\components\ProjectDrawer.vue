<template>
  <el-dialog
    v-model="drawerVisible"
    :destroy-on-close="true"
    width="450px"
    :title="`${drawerProps.title}  ${$t('project.project')}`"
  >
    <el-form
      ref="ruleFormRef"
      label-width="120px"
      label-suffix=" :"
      :rules="rules"
      :disabled="drawerProps.isView"
      :model="drawerProps.row"
      :hide-required-asterisk="drawerProps.isView"
    >
      <el-form-item :label="$t('project.projectNameLabel')" prop="username">
        <el-input v-model="drawerProps.row!.groupName" :placeholder="$t('project.projectNamePlaceholder')" clearable></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="drawerVisible = false">{{ $t("common.cancel") }}</el-button>
      <el-button v-show="!drawerProps.isView" type="primary" @click="handleSubmit">{{ $t("common.confirm") }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="ProjectDrawer">
import { ref, reactive } from "vue";
import { User } from "@/api/interface";
import { ElMessage, FormInstance } from "element-plus";
import { useI18n } from "vue-i18n";
// 定义 emit 事件
const emit = defineEmits(["closeDrawer", "submitSuccess", "refreshTreeFilter"]);

const { t } = useI18n();

const rules = reactive({
  groupName: [{ required: true, message: t("project.projectNamePlaceholder") }]
});

interface ProjectData extends Partial<User.ResUserList> {
  groupName?: string;
}

interface DrawerProps {
  title: string;
  isView: boolean;
  row: ProjectData;
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const drawerVisible = ref(false);
const drawerProps = ref<DrawerProps>({
  isView: false,
  title: "",
  row: {}
});

// 接收父组件传过来的参数
const acceptParams = (params: DrawerProps) => {
  drawerProps.value = params;
  drawerVisible.value = true;
};

// 提交数据（新增/编辑）
const ruleFormRef = ref<FormInstance>();
const handleSubmit = () => {
  ruleFormRef.value!.validate(async valid => {
    if (!valid) return;
    try {
      await drawerProps.value.api!(drawerProps.value.row);
      ElMessage.success({
        message: `${drawerProps.value.title}成功！`
      });
      // 调用刷新父组件的 TreeFilter
      emit("refreshTreeFilter");
      drawerProps.value.getTableList!();
      drawerVisible.value = false;
    } catch (error) {
      console.log(error);
    }
  });
};

defineExpose({
  acceptParams
});
</script>
