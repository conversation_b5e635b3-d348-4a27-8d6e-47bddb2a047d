<template>
  <div class="dark-mode-switch">
    <el-tooltip :content="globalStore.isDark ? t('header.toggleLightMode') : t('header.toggleDarkMode')" placement="bottom">
      <i class="toolBar-icon" @click="toggleDarkMode">
        <svg v-if="globalStore.isDark" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M12 17q-2.075 0-3.538-1.463T7 12q0-2.075 1.463-3.538T12 7q2.075 0 3.538 1.463T17 12q0 2.075-1.463 3.538T12 17M2 13h2q.425 0 .713-.288T5 12q0-.425-.288-.713T4 11H2q-.425 0-.713.288T1 12q0 .425.288.713T2 13m18 0h2q.425 0 .713-.288T23 12q0-.425-.288-.713T22 11h-2q-.425 0-.713.288T19 12q0 .425.288.713T20 13m-8-8q.425 0 .713-.288T13 4V2q0-.425-.288-.713T12 1q-.425 0-.713.288T11 2v2q0 .425.288.713T12 5m0 14q.425 0 .713-.288T13 18v-2q0-.425-.288-.713T12 15q-.425 0-.713.288T11 16v2q0 .425.288.713T12 19M5.65 7.05L4.575 6q-.3-.275-.288-.7t.288-.725q.3-.3.725-.3t.7.3L7.05 5.65q.275.3.275.7t-.275.7q-.275.3-.687.288T5.65 7.05M18.35 7.05q-.275-.3-.275-.7t.275-.7l1.075-1.05q.3-.3.713-.3t.712.3q.3.3.3.725t-.3.7L19.4 7.05q-.3.275-.7.288t-.7-.288M16.95 19.4l1.05 1.075q.3.3.3.713t-.3.712q-.3.3-.725.3t-.7-.3L15.55 20.35q-.275-.3-.275-.7t.275-.7q.275-.3.687-.288t.713.288M4.575 19.4q-.3-.275-.288-.687t.288-.713L5.65 16.95q.3-.275.7-.275t.7.275q.3.275.288.688t-.288.712L6 19.4q-.3.3-.725.3t-.7-.3"
          />
        </svg>
        <svg v-else xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M12 21q-3.75 0-6.375-2.625T3 12q0-3.75 2.625-6.375T12 3q.35 0 .688.025t.662.075q-1.025.725-1.638 1.888T11.1 7.5q0 2.25 1.575 3.825T16.5 12.9q1.375 0 2.525-.613T20.9 10.65q.05.325.075.662T21 12q0 3.75-2.625 6.375T12 21"
          />
        </svg>
      </i>
    </el-tooltip>
  </div>
</template>

<script setup lang="ts" name="DarkMode">
import { useTheme } from "@/hooks/useTheme";
import { useGlobalStore } from "@/stores/modules/global";
import { useI18n } from "vue-i18n";

const { switchDark } = useTheme();
const globalStore = useGlobalStore();
const { t } = useI18n();

const toggleDarkMode = () => {
  globalStore.setGlobalState("isDark", !globalStore.isDark);
  switchDark();
};
</script>

<style scoped lang="scss">
.dark-mode-switch {
  cursor: pointer;
  transition: all 0.3s ease;

  .toolBar-icon {
    font-size: 20px;

    &:hover {
      color: var(--el-color-primary);
    }
  }
}
</style>
