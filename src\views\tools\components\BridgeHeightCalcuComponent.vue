<template>
  <div class="bridge-calculator-container">
    <div class="calculator-card">
      <div class="card-header">
        <h3>{{ $t("common.bridgeHeightCalculator") }}</h3>
        <p class="description">{{ $t("common.bridgeHeightCalculatorDescription") }}</p>
      </div>

      <div class="diagram-container">
        <div class="image-wrapper">
          <img :src="bridgeDiagram" class="diagram-image" />
        </div>
        <div class="formula-box">
          <div class="formula-title">{{ $t("common.calculationTip") }}</div>
          <div class="formula-description">{{ $t("common.whereFormula") }}</div>
        </div>
      </div>

      <div class="form-container">
        <el-form ref="formRef" :model="formData" :rules="rules" class="calculator-form" label-position="top">
          <el-form-item :label="t('common.frequencyBandSelection')" prop="frequencyBand">
            <el-select
              v-model="formData.frequencyBand"
              class="form-select"
              :placeholder="t('common.selectFrequencyBand')"
              size="large"
            >
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item :label="t('common.distanceAPtoClientBridge') + ' (d)'" prop="distance">
            <el-input v-model="formData.distance" type="number" :placeholder="t('common.enterDistance')" class="form-input">
              <template #append>{{ $t("common.meters") }}</template>
            </el-input>
          </el-form-item>
          <el-form-item :label="t('common.distanceObstacleToClientBridge')" prop="duration1" class="obstacle-distance-item">
            <el-input v-model="formData.duration1" type="number" :placeholder="t('common.enterDistance')" class="form-input">
              <template #append>{{ $t("common.meters") }}</template>
            </el-input>
          </el-form-item>
          <el-form-item :label="t('common.obstacleHeight')" prop="obstacleHeight">
            <el-input
              v-model="formData.obstacleHeight"
              type="number"
              :placeholder="t('common.enterObstacleHeight')"
              class="form-input"
            >
              <template #append>{{ $t("common.meters") }}</template>
            </el-input>
          </el-form-item>
        </el-form>
      </div>

      <div class="button-container">
        <el-button
          type="primary"
          :icon="Search"
          @click="calculatorHeight"
          class="action-button search-button"
          :loading="isLoading"
        >
          {{ $t("common.calculateResult") }}
        </el-button>
        <el-button type="danger" :icon="Delete" @click="clearForm" class="action-button clear-button">
          {{ $t("common.clear") }}
        </el-button>
      </div>
    </div>

    <div class="result-card" :class="{ 'has-result': searchResult.data > 0 }">
      <div class="card-header">
        <h3>{{ $t("common.calculateResult") }}</h3>
        <el-tag v-if="isLoading" type="info">{{ $t("common.calculating") }}</el-tag>
      </div>
      <transition name="fade">
        <div class="result-content" v-if="searchResult.data > 0">
          <div class="result-icon">
            <el-icon class="success-icon"><CircleCheckFilled /></el-icon>
          </div>
          <div class="result-details">
            <div class="result-label">{{ $t("common.bridgeHeightResult") }}</div>
            <div class="result-value">
              <span class="highlight">{{ searchResult.data }}</span> {{ $t("common.meters") }}
            </div>
            <div class="result-tip">{{ $t("common.bridgeHeightCalculatorTip") }}</div>
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<script setup lang="ts" name="BridgeHeightCalcuComponent">
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import { Delete, Search, CircleCheckFilled } from "@element-plus/icons-vue";
import { calculateHeightOfBridge } from "@/api/modules/tools";
import { ElMessage, FormInstance } from "element-plus";
import bridgeDiagram from "@/assets/images/bridge-diagram.jpeg";
const { t } = useI18n();
// Define interface for IP info response
interface CalcuResponse {
  code: string;
  msg: string;
  data: number;
}

interface formData {
  frequencyBand: number;
  distance: number;
  duration1: number;
  obstacleHeight: number;
}

const formData = ref<formData>({
  frequencyBand: 1,
  distance: 20,
  duration1: 10,
  obstacleHeight: 10
});

const rules = ref({
  frequencyBand: [{ required: true, message: t("common.selectFrequencyBand"), trigger: "change" }],
  distance: [{ required: true, message: t("common.enterDistance"), trigger: "blur" }],
  duration1: [{ required: true, message: t("common.enterDistance"), trigger: "blur" }],
  obstacleHeight: [{ required: true, message: t("common.enterObstacleHeight"), trigger: "blur" }]
});

const options = [
  {
    value: 1,
    label: "2.4G"
  },
  {
    value: 2,
    label: "5G band1"
  },
  {
    value: 3,
    label: "5G band2"
  }
];

// 响应式状态
const isLoading = ref(false);

// eslint-disable-next-line @typescript-eslint/no-empty-function
const clearForm = () => {
  formRef.value?.resetFields();
  searchResult.value = {
    data: 0
  };
};

const searchResult = ref({
  data: 0
});

const formRef = ref<FormInstance>();

const calculatorHeight = async () => {
  if (!formRef.value) return;

  // 验证表单 - 使用 await 等待验证结果
  try {
    await formRef.value.validate();
  } catch (error) {
    // 验证失败
    return;
  }

  isLoading.value = true;
  searchResult.value = {
    data: 0
  };

  try {
    // 创建一个新对象，避免传递引用类型导致循环引用
    const params = {
      frequencyBand: formData.value.frequencyBand,
      distance: formData.value.distance,
      duration1: formData.value.duration1,
      obstacleHeight: formData.value.obstacleHeight
    };

    console.log("params:", params);
    const rawResponse = await calculateHeightOfBridge(params);
    const response = rawResponse as unknown as CalcuResponse;
    if (response.code === "200") {
      searchResult.value = {
        data: response.data
      };
    } else {
      ElMessage.warning(t("common.noDataTip"));
      searchResult.value = {
        data: 0
      };
    }
  } catch (error) {
    console.error(t("common.operationFail"), error);
    ElMessage.error(t("common.operationFail"));
    searchResult.value = {
      data: 0
    };
  } finally {
    isLoading.value = false;
  }
};

// 重置状态
const resetState = () => {
  console.log("重置状态");
  searchResult.value = {
    data: 0
  };
  isLoading.value = false;
};

// 将重置方法暴露给父组件
defineExpose({
  resetState
});
</script>

<style lang="scss" scoped>
@import "../index";

.bridge-calculator-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.calculator-card,
.result-card {
  background-color: var(--el-bg-color);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  padding: 24px;
  transition: all 0.3s ease;
}

.calculator-card:hover,
.result-card:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.diagram-container {
  margin-bottom: 30px;
  display: flex;
  flex-direction: column;
  width: 100%;

  .image-wrapper {
    width: 100%;
    height: 450px;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: visible;
    border-radius: 8px;
    margin-bottom: 16px;
  }

  .diagram-image {
    width: 100%;
    height: auto;
    max-height: 450px;
    object-fit: contain;
  }

  .formula-box {
    background-color: var(--el-fill-color-light);
    border-radius: 8px;
    padding: 16px;
    width: 100%;
    border-left: 4px solid var(--el-color-primary);

    .formula-title {
      font-weight: 600;
      margin-bottom: 8px;
      color: var(--el-text-color-primary);
    }

    .formula-description {
      font-size: 14px;
      color: var(--el-text-color-secondary);
      line-height: 1.5;
    }
  }
}

.form-container {
  margin-bottom: 20px;

  .calculator-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }

    :deep(.el-form-item__label) {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    :deep(.obstacle-distance-item .el-form-item__label) {
      font-size: 14px;
    }
  }

  .form-input,
  .form-select {
    width: 100%;
  }
}

.button-container {
  display: flex;
  gap: 16px;
  margin-top: 24px;

  .action-button {
    padding: 12px 24px;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  .search-button {
    flex: 2;
  }

  .clear-button {
    flex: 1;
  }
}

.result-card {
  overflow: hidden;

  &.has-result {
    border: 1px solid var(--el-color-success-light-5);
    background-color: var(--el-color-success-light-9);
  }

  .result-content {
    display: flex;
    align-items: center;
    padding: 16px;
    background-color: var(--el-fill-color-light);
    border-radius: 8px;
    margin-top: 16px;

    .result-icon {
      margin-right: 20px;

      .success-icon {
        font-size: 48px;
        color: var(--el-color-success);
      }
    }

    .result-details {
      flex: 1;

      .result-label {
        font-size: 16px;
        color: var(--el-text-color-secondary);
        margin-bottom: 8px;
      }

      .result-value {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        margin-bottom: 8px;
      }

      .result-tip {
        font-size: 14px;
        color: var(--el-text-color-secondary);
        font-style: italic;
      }
    }
  }
}

.highlight {
  color: var(--el-color-success);
  font-weight: bold;
  font-size: 1.2em;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.card-header {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
}

.description {
  color: var(--el-text-color-secondary);
  font-size: 14px;
  margin: 8px 0 0 0;
}

.ip-input-container {
  margin-bottom: 20px;
}

.input-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
}

.ip-input {
  display: flex;
  align-items: center;
  background-color: var(--el-fill-color-blank);
  border-radius: 8px;
  padding: 10px 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 0;
}

.ip-part {
  position: relative;
  margin: 0 2px;
  display: flex;
  align-items: center;
}

.ip-part-input {
  width: 60px;
  transition: all 0.3s ease;
}

.ip-part-input :deep(.el-input__inner) {
  text-align: center;
  font-family: monospace;
  font-weight: 500;
  letter-spacing: 1px;
}

.split {
  margin: 0 2px;
  font-weight: 600;
  color: var(--el-text-color-secondary);
}

.button-container {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.action-button {
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-button {
  flex: 2;
}

.clear-button {
  flex: 1;
}

.result-content {
  background-color: var(--el-fill-color-light);
  border-radius: 8px;
  padding: 16px;
}

.result-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.result-item {
  padding: 12px;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.result-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.result-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
}

.result-value {
  font-size: 16px;
  color: var(--el-text-color-primary);
  word-break: break-word;
}

.flag-image {
  width: 40px;
  height: 20px;
  object-fit: cover;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loading-container {
  padding: 16px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .ip-input {
    flex-wrap: wrap;
    justify-content: center;
  }

  .ip-part {
    margin: 5px;
  }

  .result-grid {
    grid-template-columns: 1fr;
  }
}
</style>
