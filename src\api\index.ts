import axios, { AxiosInstance, AxiosError, AxiosRequestConfig, InternalAxiosRequestConfig, AxiosResponse } from "axios";
import { showFullScreenLoading, tryHideFullScreenLoading } from "@/components/Loading/fullScreen";
import { LOGIN_URL } from "@/config";
import { ElMessage } from "element-plus";
import { ResultData } from "@/api/interface";
import { ResultEnum } from "@/enums/httpEnum";
import { checkStatus } from "./helper/checkStatus";
import { AxiosCanceler } from "./helper/axiosCancel";
import { useUserStore } from "@/stores/modules/user";
import router from "@/routers";
import i18n from "@/languages/index";
const t = i18n.global.t;

export interface CustomAxiosRequestConfig extends InternalAxiosRequestConfig {
  loading?: boolean;
  cancel?: boolean;
  showErrorMessage?: boolean; // 新增配置项，控制是否显示错误信息
}

// 扩展 AxiosRequestConfig 接口，添加 showErrorMessage 属性
declare module "axios" {
  interface AxiosRequestConfig {
    showErrorMessage?: boolean;
  }
}

const config = {
  // 默认地址请求地址，可在 .env.** 文件中修改
  baseURL: import.meta.env.VITE_API_URL as string,
  // 设置超时时间
  timeout: ResultEnum.TIMEOUT as number,
  // 跨域时候允许携带凭证
  withCredentials: true
};

const axiosCanceler = new AxiosCanceler();

class RequestHttp {
  service: AxiosInstance;
  public constructor(config: AxiosRequestConfig) {
    // instantiation
    this.service = axios.create(config);

    /**
     * @description 请求拦截器
     * 客户端发送请求 -> [请求拦截器] -> 服务器
     * token校验(JWT) : 接受服务器返回的 token,存储到 vuex/pinia/本地储存当中
     */
    this.service.interceptors.request.use(
      (config: CustomAxiosRequestConfig) => {
        const userStore = useUserStore();
        // 重复请求不需要取消，在 api 服务中通过指定的第三个参数: { cancel: false } 来控制
        config.cancel ??= true;
        config.cancel && axiosCanceler.addPending(config);
        // 当前请求不需要显示 loading，在 api 服务中通过指定的第三个参数: { loading: false } 来控制
        config.loading ??= true;
        config.loading && showFullScreenLoading();
        // 默认显示错误信息，可以在请求配置中通过 { showErrorMessage: false } 来控制
        config.showErrorMessage ??= true;
        if (config.headers && typeof config.headers.set === "function") {
          config.headers.set("Authorization", "Bearer " + userStore.token);
          // 手动设置完整的语言标签
          const language = i18n.global.locale.value === "en" ? "en_US" : "zh_CN";
          config.headers.set("Accept-Language", language);
        }
        return config;
      },
      (error: AxiosError) => {
        return Promise.reject(error);
      }
    );

    /**
     * @description 响应拦截器
     *  服务器换返回信息 -> [拦截统一处理] -> 客户端JS获取到信息
     */
    this.service.interceptors.response.use(
      (response: AxiosResponse & { config: CustomAxiosRequestConfig }) => {
        const { data, config } = response;

        const userStore = useUserStore();
        axiosCanceler.removePending(config);
        config.loading && tryHideFullScreenLoading();

        // 如果登录失效，清除 token
        if (data.code === ResultEnum.OVERDUE) {
          userStore.setToken("");
          router.replace(LOGIN_URL);
          ElMessage.error(data.msg);
          return Promise.reject(data);
        }

        // 检查是否存在 Authorization 字段
        if (data.Authorization) {
          const token = data.Authorization.split(" ")[1]; // 获取 Bearer 后的 token 部分
          userStore.setToken(token); // 将 token 存储到 vuex 或 pinia
          return {
            access_token: token, // 返回映射的 access_token
            msg: data.msg, // 保留其他返回字段
            code: data.code // 保留 code 字段
          };
        }

        // 如果没有 Authorization 字段，返回原始数据
        return data;
      },
      (error: AxiosError) => {
        const { response } = error;
        tryHideFullScreenLoading();
        // 如果配置中允许显示错误信息，则显示
        if (config?.showErrorMessage) {
          // 请求超时 & 网络错误的处理
          if (error.message.includes("timeout")) ElMessage.error(t("error.requestTimeoutTip"));
          if (error.message.includes("Network Error")) ElMessage.error(t("error.networkErrorTip"));
          if (response) checkStatus(response.status);
        }

        // 网络断开时的处理
        if (!window.navigator.onLine) router.replace("/500");

        return Promise.reject(error);
      }
    );
  }

  /**
   * @description 常用请求方法封装
   */
  get<T>(url: string, params?: object, _object = {}): Promise<ResultData<T>> {
    return this.service.get(url, { params, ..._object });
  }
  post<T>(url: string, params?: object | string, _object = {}): Promise<ResultData<T>> {
    return this.service.post(url, params, _object);
  }
  put<T>(url: string, params?: object, _object = {}): Promise<ResultData<T>> {
    return this.service.put(url, params, _object);
  }
  delete<T>(url: string, params?: any, _object = {}): Promise<ResultData<T>> {
    return this.service.delete(url, { params, ..._object });
  }
  download(url: string, params?: object, _object = {}): Promise<BlobPart> {
    return this.service.post(url, params, { ..._object, responseType: "blob" });
  }
}

export default new RequestHttp(config);
