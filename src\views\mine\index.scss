// 反馈对话框样式
.feedback-dialog {
  :deep(.el-dialog) {
    overflow: hidden;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
    transform: translateY(0);
    transition:
      transform 0.3s ease,
      box-shadow 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .el-dialog__header {
      padding: 24px 30px;
      margin: 0;
      background: var(--el-bg-color-overlay);
      border-bottom: 1px solid var(--el-border-color-lighter);
      position: relative;

      &::after {
        content: "";
        position: absolute;
        bottom: -1px;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 3px;
        background: linear-gradient(90deg, var(--el-color-primary), var(--el-color-primary-light-5));
        border-radius: 3px;
      }

      .el-dialog__title {
        font-size: 20px;
        font-weight: 600;
        color: var(--el-color-primary);
        letter-spacing: 0.5px;
      }
    }

    .el-dialog__body {
      padding: 30px;
      .el-form {
        width: 100%;
        .el-form-item {
          margin-bottom: 28px;
          &:last-child {
            margin-bottom: 0;
          }
          .el-form-item__label {
            padding-bottom: 10px;
            font-weight: 500;
            color: var(--el-text-color-regular);
            font-size: 15px;
            letter-spacing: 0.3px;
          }
          .el-input__wrapper {
            width: 100%;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
            border-radius: 10px;
            padding: 2px 15px;
            transition: all 0.3s ease;
            &:hover {
              box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
              transform: translateY(-2px);
            }
            &.is-focus {
              box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
            }
          }
          .el-textarea__inner {
            width: 100%;
            min-height: 140px;
            padding: 15px;
            font-size: 15px;
            line-height: 1.6;
            resize: vertical;
            border-radius: 12px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;

            &:hover {
              box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            }

            &:focus {
              box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
            }
          }
        }
        .form-foot {
          display: flex;
          gap: 15px;
          justify-content: flex-end;
          margin-top: 40px;
          .el-button {
            padding: 12px 28px;
            font-weight: 500;
            border-radius: 12px;
            transition: all 0.3s ease;
            &:hover {
              box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
              transform: translateY(-3px);
            }
            &.el-button--primary {
              background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
              border: none;
              &:hover {
                background: linear-gradient(135deg, var(--el-color-primary-light-3), var(--el-color-primary));
              }
            }
          }
        }
      }
    }
  }
}

// 用户信息页样式
.profile-container {
  /* 基本容器样式 */
  display: flex;
  justify-content: center;
  min-height: calc(100vh - 60px);
  padding: 30px 20px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4ecf7 50%, #d3e0f7 100%);
  border-radius: 24px;
  width: 100%;
  transition: all 0.5s ease;

  /* 装饰元素样式 */
  .decoration-element {
    position: absolute;
    border-radius: 50%;
    filter: blur(40px);
    z-index: 0;
    pointer-events: none;
  }

  .decoration-top-right {
    top: -100px;
    right: -100px;
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(100, 149, 237, 0.3) 0%, rgba(100, 149, 237, 0) 70%);
    animation: float 20s ease-in-out infinite alternate;
  }

  .decoration-bottom-left {
    bottom: -150px;
    left: -150px;
    width: 500px;
    height: 500px;
    background: radial-gradient(circle, rgba(144, 238, 144, 0.3) 0%, rgba(144, 238, 144, 0) 70%);
    animation: float 25s ease-in-out infinite alternate-reverse;
  }

  .decoration-circle-1 {
    top: 20%;
    left: 10%;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(255, 182, 193, 0.2) 0%, rgba(255, 182, 193, 0) 70%);
    animation: pulse 8s ease-in-out infinite;
  }

  .decoration-circle-2 {
    bottom: 15%;
    right: 10%;
    width: 180px;
    height: 180px;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.2) 0%, rgba(255, 215, 0, 0) 70%);
    animation: pulse 12s ease-in-out infinite reverse;
  }

  /* 内容区域样式 */
  .profile-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 800px;
    gap: 24px;
    z-index: 1;
    padding: 10px;
  }

  /* 卡片基本样式 */
  .profile-card {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.5);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    padding: 30px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;

    &:hover {
      box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
      transform: translateY(-5px);
      border-color: rgba(var(--el-color-primary-rgb), 0.3);
    }

    /* 卡片光效 */
    &::before {
      content: "";
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 80%);
      transform: rotate(30deg);
      z-index: 0;
      transition: all 0.5s ease;
      opacity: 0;
    }

    &:hover::before {
      opacity: 1;
      animation: shine 3s infinite linear;
    }

    /* 标题样式 */
    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--el-color-primary);
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid rgba(var(--el-color-primary-rgb), 0.1);
      position: relative;

      &::after {
        content: "";
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 60px;
        height: 3px;
        background: linear-gradient(90deg, var(--el-color-primary), var(--el-color-primary-light-5));
        border-radius: 3px;
        transition: width 0.3s ease;
      }
    }

    &:hover .section-title::after {
      width: 100px;
    }
  }

  /* 主卡片样式 */
  .main-card {
    .profile-header {
      margin-bottom: 30px;
    }

    .avatar-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      gap: 20px;
      margin-bottom: 20px;
    }

    .avatar-wrapper {
      position: relative;
      flex-shrink: 0;

      .el-avatar {
        width: 120px;
        height: 120px;
        border: 5px solid rgba(255, 255, 255, 0.8);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        background: rgba(255, 255, 255, 0.25);
        position: relative;
        z-index: 2;
        margin-bottom: 5px;

        &:hover {
          border-color: var(--el-color-primary-light-5);
          transform: scale(1.05) rotate(5deg);
          box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }
      }

      .avatar-status {
        position: absolute;
        bottom: 10px;
        right: 10px;
        width: 22px;
        height: 22px;
        border-radius: 50%;
        background-color: #67c23a;
        border: 3px solid #fff;
        box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.3);
        animation: pulse 2s infinite;
        z-index: 3;
      }
    }

    .user-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      width: 100%;

      .user-name {
        font-size: 24px;
        font-weight: 600;
        color: var(--el-color-primary-dark-2);
        margin: 0 0 10px;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;

        &.empty {
          color: var(--el-text-color-secondary);
          font-style: italic;
          font-size: 20px;
        }
      }

      .user-role {
        margin-top: 5px;
        display: flex;
        justify-content: center;

        .el-tag {
          padding: 6px 14px;
          font-size: 13px;
          border-radius: 20px;
          background: rgba(var(--el-color-success-rgb), 0.1);
          border-color: rgba(var(--el-color-success-rgb), 0.2);
          color: var(--el-color-success);
          transition: all 0.3s ease;

          &:hover {
            background: rgba(var(--el-color-success-rgb), 0.15);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(var(--el-color-success-rgb), 0.2);
          }

          .el-icon {
            margin-right: 5px;
            font-size: 14px;
          }
        }
      }
    }

    /* 信息项样式 */
    .profile-section {
      padding-top: 10px;
    }

    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      padding: 15px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 12px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.4);
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05);
        border-color: rgba(var(--el-color-primary-rgb), 0.2);
      }

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        width: 80px;
        font-size: 15px;
        font-weight: 500;
        color: var(--el-text-color-regular);
        flex-shrink: 0;
      }

      .info-content {
        flex-grow: 1;
        padding: 0 15px;

        .info-value {
          display: flex;
          align-items: center;
          gap: 10px;
          font-size: 16px;
          color: var(--el-text-color-primary);

          .el-icon {
            color: var(--el-color-primary);
            font-size: 18px;
          }

          &.empty {
            color: var(--el-text-color-secondary);
            font-style: italic;
          }
        }

        .input-field {
          width: 100%;
          .el-input__wrapper {
            background: rgba(255, 255, 255, 0.5);
            border-radius: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);

            &:hover {
              box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
              transform: translateY(-2px);
            }

            &.is-focus {
              box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
            }
          }

          .input-icon {
            color: var(--el-color-primary);
            font-size: 16px;
          }
        }
      }

      .info-action {
        flex-shrink: 0;
        width: 40px;
        display: flex;
        justify-content: center;

        .action-btn {
          width: 36px;
          height: 36px;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          transition: all 0.3s ease;

          .el-icon {
            font-size: 16px;
          }

          &:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 5px 15px rgba(var(--el-color-primary-rgb), 0.2);
          }
        }
      }
    }
  }

  /* 快捷链接卡片样式 */
  .links-card {
    .quick-links {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 20px;

      @media (max-width: 768px) {
        grid-template-columns: repeat(2, 1fr);
      }

      @media (max-width: 480px) {
        grid-template-columns: 1fr;
      }
    }

    .link-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 16px;
      border: 1px solid rgba(255, 255, 255, 0.3);
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      cursor: pointer;
      height: 120px;

      &:hover {
        background: rgba(255, 255, 255, 0.4);
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        border-color: rgba(var(--el-color-primary-rgb), 0.3);
      }

      .link-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: rgba(var(--el-color-primary-rgb), 0.1);
        margin-bottom: 12px;
        transition: all 0.3s ease;

        .el-icon {
          font-size: 24px;
          color: var(--el-color-primary);
        }
      }

      .link-text {
        font-size: 15px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        text-align: center;
        transition: all 0.3s ease;
      }

      &:hover {
        .link-icon {
          background: rgba(var(--el-color-primary-rgb), 0.2);
          transform: scale(1.1) rotate(5deg);
        }

        .link-text {
          color: var(--el-color-primary);
        }
      }
    }
  }

  @media (max-width: 768px) {
    padding: 15px;
  }

  /* 退出登录按钮样式 */
  .logout-container {
    display: flex;
    justify-content: center;
    margin-top: 10px;
    margin-bottom: 20px;

    .logout-button {
      min-width: 200px;
      padding: 12px 30px;
      font-size: 16px;
      font-weight: 600;
      color: #fff;
      background: linear-gradient(45deg, #ff5252, #ff7675);
      border: none;
      border-radius: 50px;
      box-shadow: 0 10px 20px rgba(255, 82, 82, 0.3);
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      position: relative;
      overflow: hidden;

      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transform: translateX(-100%);
        transition: transform 0.6s ease;
      }

      .el-icon {
        margin-right: 10px;
        font-size: 18px;
      }

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(255, 82, 82, 0.4);
        background: linear-gradient(45deg, #ff3838, #ff5252);

        &::after {
          transform: translateX(100%);
        }
      }

      &:active {
        transform: translateY(2px);
        box-shadow: 0 5px 15px rgba(255, 82, 82, 0.4);
      }
    }
  }
}

/* 动画关键帧 */
@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
  100% {
    transform: translateY(0) rotate(0deg);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.4;
    transform: scale(1);
  }
}

@keyframes shine {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

// 对话框样式
:deep(.el-dialog) {
  overflow: hidden;
  border-radius: 16px;
  /* 毕玻璃效果 */
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  width: 90% !important;
  max-width: 800px;

  .el-dialog__header {
    padding: 20px 24px;
    margin: 0;
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  }
  .el-dialog__body {
    padding: 24px;
    background: rgba(255, 255, 255, 0.05);
  }
  .el-dialog__footer {
    padding: 16px 24px;
    background: rgba(255, 255, 255, 0.1);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
  }
}

/* 黑暗模式样式 */
html.dark {
  .profile-container {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d3748 70%, #364156 100%);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);

    /* 装饰元素 - 黑暗模式 */
    .decoration-top-right {
      background: radial-gradient(circle, rgba(255, 100, 100, 0.3) 0%, rgba(255, 100, 100, 0) 70%);
    }

    .decoration-bottom-left {
      background: radial-gradient(circle, rgba(100, 200, 100, 0.3) 0%, rgba(100, 200, 100, 0) 70%);
    }

    .decoration-circle-1 {
      background: radial-gradient(circle, rgba(100, 149, 237, 0.3) 0%, rgba(100, 149, 237, 0) 70%);
    }

    .decoration-circle-2 {
      background: radial-gradient(circle, rgba(255, 165, 0, 0.2) 0%, rgba(255, 165, 0, 0) 70%);
    }

    /* 卡片样式 - 黑暗模式 */
    .profile-card {
      background: rgba(30, 30, 30, 0.7);
      border: 1px solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);

      &:hover {
        background: rgba(40, 40, 40, 0.8);
        border-color: rgba(var(--el-color-primary-rgb), 0.3);
      }

      .section-title {
        color: var(--el-color-primary-light-3);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }
    }

    /* 头像样式 - 黑暗模式 */
    .avatar-wrapper .el-avatar {
      border-color: rgba(255, 255, 255, 0.2);
      background: rgba(40, 40, 40, 0.5);

      &:hover {
        border-color: var(--el-color-primary-light-5);
      }
    }

    .user-name {
      color: var(--el-color-primary-light-3);
    }

    /* 信息项样式 - 黑暗模式 */
    .info-item {
      background: rgba(40, 40, 40, 0.5);
      border: 1px solid rgba(255, 255, 255, 0.1);

      &:hover {
        background: rgba(50, 50, 50, 0.6);
        border-color: rgba(var(--el-color-primary-rgb), 0.3);
      }

      .info-label {
        color: var(--el-text-color-secondary);
      }

      .info-value {
        color: var(--el-text-color-primary);

        .el-icon {
          color: var(--el-color-primary-light-3);
        }

        &.empty {
          color: var(--el-text-color-secondary);
        }
      }

      .input-field .el-input__wrapper {
        background: rgba(30, 30, 30, 0.7);
      }
    }

    /* 链接项样式 - 黑暗模式 */
    .link-item {
      background: rgba(40, 40, 40, 0.5);
      border: 1px solid rgba(255, 255, 255, 0.1);

      &:hover {
        background: rgba(50, 50, 50, 0.6);
        border-color: rgba(var(--el-color-primary-rgb), 0.3);
      }

      .link-icon {
        background: rgba(var(--el-color-primary-rgb), 0.2);
      }

      .link-text {
        color: var(--el-text-color-primary);
      }

      &:hover {
        .link-icon {
          background: rgba(var(--el-color-primary-rgb), 0.3);
        }

        .link-text {
          color: var(--el-color-primary-light-3);
        }
      }
    }

    /* 退出按钮 - 黑暗模式 */
    .logout-button {
      background: linear-gradient(45deg, #ff3838, #ff5252);
      box-shadow: 0 10px 20px rgba(255, 82, 82, 0.4);

      &:hover {
        background: linear-gradient(45deg, #ff5252, #ff7675);
        box-shadow: 0 15px 30px rgba(255, 82, 82, 0.5);
      }
    }
  }

  /* 对话框样式 - 黑暗模式 */
  :deep(.el-dialog) {
    background: rgba(30, 30, 30, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

    .el-dialog__header {
      background: rgba(40, 40, 40, 0.5);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .el-dialog__body {
      background: rgba(30, 30, 30, 0.3);
    }

    .el-dialog__footer {
      background: rgba(40, 40, 40, 0.5);
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }
  }
}

/* 动画关键帧 */
@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
  100% {
    transform: translateY(0) rotate(0deg);
  }
}

@keyframes shine {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}
