export namespace Configuration {
  interface SystemConfig {
    reboot: {
      week: string; // 每周的重启设置（如 "*" 表示每天）
      rateDelay: number; // 延迟时间
      time: string; // 重启时间
      enabled: number; // 是否启用重启（1：启用，0：禁用）
    };
    userList: Array<{
      mode: number; // 用户模式
      week: string; // 用户活跃的周时间段
      name: string; // 用户名
      blackList: number; // 是否在黑名单中（0：否，1：是）
      rxLimit: number; // 接收带宽限制
      online: number; // 是否在线（0：不在线，1：在线）
      beginTime: string; // 可用开始时间
      endTime: string; // 可用结束时间
      ipaddr: string; // 用户IP地址
      macaddr: string; // 用户MAC地址
      internet: number; // 是否启用互联网连接（0：禁用，1：启用）
      txLimit: number; // 发送带宽限制
    }>;
    led: {
      mode: string; // LED 模式（例如 "on"）
      beginTime: string; // LED 开始时间
      endTime: string; // LED 结束时间
    };
    sysPassword: string;
    reSysPassword: string;
    swPort: Array<swPort>;
    swPoe: Array<swPoe>;
    swVlan: Array<swVlan>;
    swQos: Array<swQos>;
    sysSave: number; // 保存配置
    swStorm: Array<swStorm>;
  }

  interface WirelessConfig {
    wifiTime: {
      week: string; // 无线时间设置，表示一周中的时间段
      beginTime: string; // 无线开始时间
      endTime: string; // 无线结束时间
      enabled: number; // 是否启用无线时间（0：禁用，1：启用）
    };
    radio1: {
      hidden: number; // 是否隐藏 SSID（0：显示，1：隐藏）
      chanList: number[]; // 支持的频道列表
      txpower: number; // 发射功率
      channel: number; // 当前频道
      disabled: number; // 是否禁用无线（0：启用，1：禁用）
      ssid: string; // SSID 名称
      key: string; // 密码
    };
    guest: {
      rate: number; // 客户端最大速率
      wifiTime: number; // 客户端wifi时间（0：禁用）
      disabled: number; // 是否禁用（0：启用，1：禁用）
      ssid: string; // 客户端SSID名称
      key: string; // 客户端密码
      hidden: number; // 是否隐藏 SSID（0：显示，1：隐藏）
    };
    radio0: {
      hidden: number; // 是否隐藏 SSID（0：显示，1：隐藏）
      chanList: number[]; // 支持的频道列表
      txpower: number; // 发射功率
      channel: number; // 当前频道
      disabled: number; // 是否禁用无线（0：启用，1：禁用）
      ssid: string; // SSID 名称
      key: string; // 密码
    };
  }

  interface NetworkConfig {
    wan: Array<{
      password: string; // WAN连接密码
      gawa: string; // 网关地址
      netmask: string; // 子网掩码
      dns2: string; // 备用DNS
      proto: string; // 协议类型（如 "dhcp"）
      dns1: string; // 主DNS
      name: string; // WAN接口名称
      ipaddr: string; // WAN接口IP地址
      username: string; // WAN连接用户名
    }>;
    lan: {
      netmask: string; // LAN子网掩码
      ipaddr: string; // LAN IP地址
    };
    wanMax: number; // 最大WAN连接数
    workmode: string; // 工作模式（如 "ap"）
    brAp: {
      ssid: string; // WIFI名称
      key: string; // 密码, 有密码 encryption:psk-mixed+tkip+ccmp, 无密码: encryption:none
      hidden: number; // 隐藏SSID, 0: 显示, 1: 隐藏
      mode: number; // 模式, 0: 抗干扰 (HT20-SGI), 1: 普通 (HT40+SGI), >=2: 高带宽 (>=VHT80+SGI)
      channel: number; // 当前信道
      txpower: number; // 发送功率 0 -- 100
      chanList: number[]; // 信道列表, 0信道为固定信道, 只读数据 由设备动态计算返回, 对应 channel 数据的选择范围. 0: 自动信道
    };
    join: {
      ssid: string; // 加入的无线网络名称
      key: string; // 加入的无线网络密码
      radio: number; // 使用的无线电频段（0：2.4GHz，1：5GHz）
    };
    dhcp: {
      dnsenabled: number; // 是否启用DNS（0：禁用，1：启用）
      dns2: string; // 备用DNS
      dns1: string; // 主DNS
      start: number; // DHCP地址池起始地址
      limit: number; // DHCP地址池限制
    };
    brSafe: {
      mode: number; //模式: 0: 不锁定, 1: 定时锁定, 2: 一直锁定
      status: string; // 锁定状态, UNLOCK: 已解锁, LOCK: 列表已锁定
      timedown: number; // 剩余时间, mode=1 时使用
      time: number; // 不锁定时间计时 单位(秒), mode=1 时使用
    };
    swRstp: Array<{
      state: number; //1:打开，0：关闭
    }>;
    swLldp: Array<{
      state: number; //1:打开，0：关闭
    }>;
    swVlan: Array<swVlan>;
    swIsolate: Array<swIsolate>;
  }

  export interface swPort {
    name: string; // 端口名称
    port: number; // 端口号
    portenable: number; // 网口的手动开关 0:关闭, 1:打开
    describe: string; // 端口描述,最大32个字节，默认使用网口名称的值
    autoneg: number; // 自动协商, 0:强制模式, 1:自适应模式.
    extend: number; // 网口的超远距离传输开关，0：自适应模式，1：强制10M模式，json没有这个参数:不改变配置
    speed_duplex: number; // 速度和全双工
    flwctrl: number; //1:打开，0：关闭
    link: number; // 链接
  }

  export interface swPoe {
    port: number; // 端口号
    name: string; // 端口名称
    poeenable: number; // poe的状态
    poeclass: number; //poe获取分类  只读
    power: number; //poe的功率
    powerout: number; //poe的功率限制，0：af(15.4w),1:at(30w)
    totalpower: number; //该款交换机的总功率
    poewd: number; //poe看门狗的开关，1：打开，0：关闭（对应建议6）
    poetime: number; //看门狗触发的时间，默认180s范围：1-2^31
  }

  export interface swStorm {
    port: number; // 端口号
    name: string; // 端口名称
    rate1: number; // 多播
    rate2: number; // 广播
    rate3: number; // 单播
    trafficType: number; // 流量类型,0:不配置（不抑制）1:多播 2:广播 3:多播+广播 4:单播 5:多播+单播 6:广播+单播 7:多播+广播+单播
  }

  export interface swQos {
    port: number;
    name: string;
    qos: number;
  }

  export interface swVlan {
    port: number;
    name: string;
    vlanmode: number;
    pvid: number;
    permit: number[];
    untag: number[];
  }

  export interface swIsolate {
    name: string;
    downport: number;
    isolate: number[];
  }

  export interface DeviceConfig {
    system: SystemConfig;
    wireless: WirelessConfig;
    network: NetworkConfig;
  }
}
