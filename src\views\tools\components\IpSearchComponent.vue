<template>
  <div class="ip-search-container">
    <div class="search-card">
      <div class="card-header">
        <h3>{{ $t("common.ipSearch") }}</h3>
        <p class="description">{{ $t("common.ipSearchDescription") }}</p>
      </div>

      <div class="ip-input-container">
        <label class="input-label">{{ $t("common.ipAddress") }}</label>
        <div class="ip-input">
          <div v-for="(part, index) in ipParts" :key="index" class="ip-part">
            <el-input
              ref="ipInputRefs"
              v-model="ipParts[index]"
              type="text"
              :placeholder="`0-255`"
              @input="handleInput(index)"
              @keydown="handleKeydown($event as KeyboardEvent, index)"
              class="ip-part-input"
            />
            <span v-if="index !== 3" class="split">.</span>
          </div>
        </div>
      </div>

      <div class="button-container">
        <el-button type="primary" :icon="Search" @click="searchIp" class="action-button search-button" :loading="isLoading">
          {{ $t("common.search") }}
        </el-button>
        <el-button type="danger" :icon="Delete" @click="clearIp" class="action-button clear-button">
          {{ $t("common.clear") }}
        </el-button>
      </div>
    </div>

    <div class="result-card current-ip-card">
      <div class="card-header">
        <h3>{{ $t("common.yourCurrentInfo") }}</h3>
        <el-tag type="success" v-if="currentIp">{{ $t("common.detected") }}</el-tag>
        <el-tag type="warning" v-else>{{ $t("common.detecting") }}</el-tag>
      </div>

      <div class="result-content">
        <div class="result-grid">
          <div class="result-item">
            <div class="result-label">{{ $t("common.currentIp") }}</div>
            <div class="result-value">
              <el-skeleton v-if="!currentIp" :rows="1" animated />
              <span v-else>{{ currentIp }}</span>
            </div>
          </div>

          <div class="result-item">
            <div class="result-label">{{ $t("common.currentLocation") }}</div>
            <div class="result-value">
              <el-skeleton v-if="!currentLocation" :rows="1" animated />
              <span v-else>{{ currentLocation }}</span>
            </div>
          </div>

          <div class="result-item" v-if="flagUrl">
            <div class="result-label">{{ $t("common.flag") }}</div>
            <div class="result-value">
              <img :src="flagUrl" alt="Flag" class="flag-image" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="result-card search-result-card" v-if="searchResult.ip || isLoading">
      <div class="card-header">
        <h3>{{ $t("common.searchResults") }}</h3>
        <el-tag type="info" v-if="isLoading">{{ $t("common.searching") }}</el-tag>
      </div>

      <div v-if="isLoading" class="loading-container">
        <el-skeleton :rows="4" animated />
      </div>

      <div v-else class="result-content">
        <div class="result-grid">
          <div class="result-item">
            <div class="result-label">{{ $t("common.ipAddress") }}</div>
            <div class="result-value">{{ searchResult.ip || $t("common.noData") }}</div>
          </div>

          <div class="result-item">
            <div class="result-label">{{ $t("common.country") }}</div>
            <div class="result-value">{{ searchResult.country || $t("common.noData") }}</div>
          </div>

          <div class="result-item">
            <div class="result-label">{{ $t("common.city") }}</div>
            <div class="result-value">{{ searchResult.city || $t("common.noData") }}</div>
          </div>

          <div class="result-item">
            <div class="result-label">{{ $t("common.isp") }}</div>
            <div class="result-value">{{ searchResult.isp || $t("common.noData") }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="IpSearchComponent">
import { ref, onMounted, computed } from "vue";
import { useI18n } from "vue-i18n";
import axios from "axios";
import { Delete, Search } from "@element-plus/icons-vue";
import { getIpInfo } from "@/api/modules/tools";
import { useGlobalStore } from "@/stores/modules/global";
import { ElMessage } from "element-plus";

// Define interface for IP info response
interface IpInfoResponse {
  code: string;
  msg: string;
  country: string;
  region: string;
  city: string;
  flag: {
    img: string;
  };
  connection: {
    isp: string;
  };
}

const { t } = useI18n();
const globalStore = useGlobalStore();
const isChinese = computed(() => globalStore.language === "zh");

// 响应式状态
const ipParts = ref(["", "", "", ""]);
const ipInputRefs = ref<HTMLElement[]>([]);
const currentIp = ref("");
const currentLocation = ref("");
const flagUrl = ref<string | null>(null);
const isLoading = ref(false);
const searchResult = ref({
  ip: "",
  country: "",
  city: "",
  isp: ""
});

// 方法
const handleInput = (index: number) => {
  let value = ipParts.value[index];
  value = value.replace(/[^0-9]/g, "");
  ipParts.value[index] = value;

  if (parseInt(value) > 255) {
    ipParts.value[index] = "255";
  }

  if (value.length >= 3 && index < 3) {
    ipInputRefs.value[index + 1]?.focus();
  }
};

const clearIp = () => {
  ipParts.value = ["", "", "", ""];
  ipInputRefs.value[0]?.focus();
};

const handleKeydown = (event: KeyboardEvent, index: number) => {
  if (event.key === ".") {
    event.preventDefault();
    if (ipParts.value[index] === "") {
      ipParts.value[index] = "";
    } else {
      ipParts.value[index] = ipParts.value[index].replace(".", "");
      if (ipInputRefs.value[index + 1]) {
        ipInputRefs.value[index + 1].focus();
      }
    }
  } else if (event.key === "Backspace" && ipParts.value[index].length === 0) {
    if (index > 0) {
      ipInputRefs.value[index - 1]?.focus();
    }
  }
};

const fetchCurrentIp = () => {
  console.log("获取当前 IP");
  axios
    .get("https://icanhazip.com/")
    .then(response => {
      currentIp.value = response.data.trim();
      fetchIpInfo(currentIp.value);
    })
    .catch(error => {
      console.error("获取当前 IP 失败", error);
    });
};

const fetchIpInfo = async (ip: string) => {
  try {
    const rawResponse = await getIpInfo(ip, {
      headers: {
        "Accept-Language": isChinese.value ? "zh_CN" : "en_US"
      }
    });
    const response = rawResponse as unknown as IpInfoResponse;
    if (response.code === "200") {
      currentLocation.value = `${response.country} - ${response.region} - ${response.city}`;
      const flagImg = response.flag.img;
      flagUrl.value = flagImg ? flagImg : null;
    } else {
      currentLocation.value = t("common.noDataTip");
      flagUrl.value = null;
    }
  } catch (error) {
    console.error(t("common.operationFail"), error);
    currentLocation.value = t("common.noDataTip");
    flagUrl.value = null;
  }
};

const searchIp = async () => {
  const ip = ipParts.value.join(".").trim();
  if (!ip || ip === "...") {
    ElMessage.warning(t("common.ipTip"));
    return;
  }

  isLoading.value = true;
  searchResult.value = {
    ip: "",
    country: "",
    city: "",
    isp: ""
  };

  try {
    const rawResponse = await getIpInfo(ip, {
      headers: {
        locale: isChinese.value ? "zh" : "en",
        "Accept-Language": isChinese.value ? "zh_CN" : "en_US"
      }
    });
    const response = rawResponse as unknown as IpInfoResponse;
    if (response.code === "200") {
      searchResult.value = {
        ip: ip,
        country: response.country,
        city: response.region + "-" + response.city,
        isp: response.connection.isp
      };
    } else {
      ElMessage.warning(t("common.noDataTip"));
      searchResult.value = {
        ip: "",
        country: t("common.noData"),
        city: t("common.noData"),
        isp: t("common.noData")
      };
    }
  } catch (error) {
    console.error(t("common.operationFail"), error);
    ElMessage.error(t("common.operationFail"));
    searchResult.value = {
      ip: "",
      country: t("common.noData"),
      city: t("common.noData"),
      isp: t("common.noData")
    };
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => {
  ipInputRefs.value = Array.from(document.querySelectorAll(".ip-input .el-input__inner")) as HTMLElement[];
  fetchCurrentIp();
});

// 重置状态
const resetState = () => {
  console.log("重置状态");
  ipParts.value = ["", "", "", ""];
  // currentIp.value = "";
  // currentLocation.value = "";
  // flagUrl.value = null;
  searchResult.value = {
    ip: "",
    country: "",
    city: "",
    isp: ""
  };
  isLoading.value = false;
};

// 将重置方法暴露给父组件
defineExpose({
  resetState
});
</script>

<style lang="scss" scoped>
@import "../index";

.ip-search-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.search-card,
.result-card {
  background-color: var(--el-bg-color);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  padding: 24px;
  transition: all 0.3s ease;
}

.search-card:hover,
.result-card:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.card-header {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
}

.description {
  color: var(--el-text-color-secondary);
  font-size: 14px;
  margin: 8px 0 0 0;
}

.ip-input-container {
  margin-bottom: 20px;
}

.input-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
}

.ip-input {
  display: flex;
  align-items: center;
  background-color: var(--el-fill-color-blank);
  border-radius: 8px;
  padding: 10px 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 0;
}

.ip-part {
  position: relative;
  margin: 0 2px;
  display: flex;
  align-items: center;
}

.ip-part-input {
  width: 60px;
  transition: all 0.3s ease;
}

.ip-part-input :deep(.el-input__inner) {
  text-align: center;
  font-family: monospace;
  font-weight: 500;
  letter-spacing: 1px;
}

.split {
  margin: 0 2px;
  font-weight: 600;
  color: var(--el-text-color-secondary);
}

.button-container {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.action-button {
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-button {
  flex: 2;
}

.clear-button {
  flex: 1;
}

.result-content {
  background-color: var(--el-fill-color-light);
  border-radius: 8px;
  padding: 16px;
}

.result-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.result-item {
  padding: 12px;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.result-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.result-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
}

.result-value {
  font-size: 16px;
  color: var(--el-text-color-primary);
  word-break: break-word;
}

.flag-image {
  width: 40px;
  height: 20px;
  object-fit: cover;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loading-container {
  padding: 16px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .ip-input {
    flex-wrap: wrap;
    justify-content: center;
  }

  .ip-part {
    margin: 5px;
  }

  .result-grid {
    grid-template-columns: 1fr;
  }
}
</style>
