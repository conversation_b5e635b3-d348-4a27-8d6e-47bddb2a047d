.login-container {
  height: 100%;
  min-height: 550px;
  background-color: #f0f2f5;
  background-image: url("@/assets/images/login_bg.svg");
  background-size: 100% 100%;
  background-size: cover;
  position: relative;
  overflow: hidden;

  // 动态背景渐变
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(64, 158, 255, 0.1), rgba(103, 194, 58, 0.05), rgba(230, 162, 60, 0.05));
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
    backdrop-filter: blur(5px);
    z-index: 0;
  }

  // 添加装饰元素
  &::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 80% 20%, rgba(64, 158, 255, 0.1) 0%, transparent 40%),
      radial-gradient(circle at 20% 80%, rgba(103, 194, 58, 0.1) 0%, transparent 40%);
    z-index: 0;
    opacity: 0.8;
  }

  .login-box {
    position: relative;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 96.5%;
    height: 94%;
    padding: 0 50px;
    background-color: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    z-index: 1;

    .login-actions {
      position: absolute;
      top: 13px;
      right: 18px;
      display: flex;
      align-items: center;
      gap: 15px;
      z-index: 10;

      .dark-switch,
      .language-switch {
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.1);
        }
      }
    }

    .login-left {
      width: 800px;
      margin-right: 10px;
      .login-left-img {
        width: 100%;
        height: 100%;
        filter: drop-shadow(0 10px 15px rgba(0, 0, 0, 0.1));
        transition: all 0.5s ease;

        &:hover {
          transform: translateY(-5px);
        }
      }
    }

    .login-form {
      width: 420px;
      padding: 50px 40px 45px;
      background-color: rgba(255, 255, 255, 0.5);
      backdrop-filter: blur(20px);
      border-radius: 24px;
      box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.1),
        0 3px 10px rgba(0, 0, 0, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.4);
      transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
      animation: fadeIn 0.8s ease-out;
      position: relative;
      overflow: hidden;

      // 添加装饰元素
      &::before,
      &::after {
        content: "";
        position: absolute;
        border-radius: 50%;
        z-index: 0;
        opacity: 0.4;
        transition: all 0.6s ease;
      }

      &::before {
        width: 150px;
        height: 150px;
        background: radial-gradient(circle, rgba(64, 158, 255, 0.4) 0%, rgba(64, 158, 255, 0) 70%);
        top: -30px;
        right: -30px;
      }

      &::after {
        width: 200px;
        height: 200px;
        background: radial-gradient(circle, rgba(103, 194, 58, 0.3) 0%, rgba(103, 194, 58, 0) 70%);
        bottom: -50px;
        left: -50px;
      }

      &:hover {
        box-shadow:
          0 25px 45px rgba(0, 0, 0, 0.15),
          0 5px 15px rgba(0, 0, 0, 0.07);
        transform: translateY(-7px) scale(1.01);
        background-color: rgba(255, 255, 255, 0.6);
        border-color: rgba(255, 255, 255, 0.5);

        &::before {
          transform: scale(1.2) translate(-10px, 10px);
          opacity: 0.6;
        }

        &::after {
          transform: scale(1.1) translate(10px, -10px);
          opacity: 0.5;
        }
      }

      .login-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 45px;
        position: relative;
        z-index: 1;

        .login-icon {
          width: 60px;
          height: 60px;
          filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
          transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
          animation: float 3s ease-in-out infinite;

          &:hover {
            transform: scale(1.1) rotate(8deg);
            filter: drop-shadow(0 8px 12px rgba(0, 0, 0, 0.2)) brightness(1.05);
          }
        }

        .logo-text {
          padding: 0 0 0 25px;
          margin: 0;
          font-size: 42px;
          font-weight: bold;
          background: linear-gradient(135deg, #34495e 0%, #4a6b8a 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          white-space: nowrap;
          text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
          position: relative;
          transition: all 0.3s ease;

          &::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 25px;
            width: 0;
            height: 3px;
            background: linear-gradient(90deg, #409eff, #67c23a);
            transition: width 0.5s ease;
            border-radius: 3px;
            opacity: 0.7;
          }

          &:hover::after {
            width: calc(100% - 25px);
          }
        }
      }

      .el-form-item {
        margin-bottom: 40px;
      }

      .login-btn {
        justify-items: center;
        justify-content: space-between;
        width: 100%;
        margin-top: 40px;
        white-space: nowrap;
        .el-button {
          width: 185px;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0);
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@media screen and (width <= 1250px) {
  .login-left {
    display: none;
  }
}

@media screen and (width <= 600px) {
  .login-form {
    width: 97% !important;
  }
}

// 对话框中的表单样式
.dialog-form {
  .el-form-item {
    margin-bottom: 20px;
    opacity: 1;
    transform: none;
    animation: none;
  }

  .el-input__wrapper {
    background-color: #fff;
    box-shadow: 0 0 0 1px #dcdfe6 inset !important;
    backdrop-filter: none;
    transform: none !important;

    &:hover {
      box-shadow: 0 0 0 1px #c0c4cc inset !important;
      transform: none !important;
    }

    &.is-focus {
      box-shadow: 0 0 0 1px #409eff inset !important;
    }
  }

  .el-button {
    border-radius: 4px;
    transform: none !important;

    &:hover {
      transform: none !important;
      box-shadow: none !important;
    }
  }
}

// 美化输入框样式 - 增强版
.login-form {
  .el-form-item {
    position: relative;
    overflow: hidden;

    // 添加装饰元素
    &::before {
      content: "";
      position: absolute;
      width: 100px;
      height: 100px;
      background: radial-gradient(circle, rgba(64, 158, 255, 0.2) 0%, rgba(64, 158, 255, 0) 70%);
      border-radius: 50%;
      top: -50px;
      left: -50px;
      opacity: 0;
      transition: opacity 0.5s ease;
      z-index: 0;
      pointer-events: none;
    }

    &:hover::before {
      opacity: 1;
      animation: pulse 2s infinite;
    }
  }

  .el-input {
    --el-input-hover-border-color: #409eff;
    --el-input-focus-border-color: #409eff;
    position: relative;
    z-index: 1;

    .el-input__wrapper {
      box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.3) inset;
      border-radius: 16px;
      padding: 0 15px;
      transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
      background-color: rgba(255, 255, 255, 0.25);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      overflow: hidden;

      // 添加光效应
      &::before {
        content: "";
        position: absolute;
        top: -10px;
        left: -10px;
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.5);
        filter: blur(30px);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        box-shadow:
          0 0 0 1px var(--el-input-hover-border-color) inset,
          0 5px 15px rgba(0, 0, 0, 0.1);
        transform: translateY(-3px) scale(1.01);
        background-color: rgba(255, 255, 255, 0.35);
        border-color: rgba(255, 255, 255, 0.3);

        &::before {
          opacity: 1;
          animation: shine 1.5s infinite alternate;
        }
      }

      &.is-focus {
        box-shadow:
          0 0 0 2px var(--el-input-focus-border-color) inset,
          0 8px 20px rgba(0, 0, 0, 0.1);
        background-color: rgba(255, 255, 255, 0.4);
        border-color: rgba(64, 158, 255, 0.3);
        transform: translateY(-3px) scale(1.02);
      }
    }

    .el-input__inner {
      height: 45px;
      color: #333;
      font-size: 16px;
      font-weight: 500;
      letter-spacing: 0.3px;
    }

    .el-input__icon {
      color: rgba(64, 158, 255, 0.7);
      font-size: 20px;
      transition: all 0.3s ease;
      filter: drop-shadow(0 0 2px rgba(64, 158, 255, 0.3));

      &:hover {
        transform: scale(1.1);
      }
    }

    &:hover .el-input__icon {
      color: var(--el-input-hover-border-color);
      animation: pulse 2s infinite;
    }
  }

  // 密码框特殊样式
  .el-input--password {
    .el-input__wrapper {
      padding-right: 45px;
    }

    .el-input__icon.is-clickable {
      color: rgba(103, 194, 58, 0.7);

      &:hover {
        color: rgba(103, 194, 58, 1);
      }
    }
  }

  // 添加按钮动效
  .login-btn {
    .el-button {
      position: relative;
      overflow: hidden;
      transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
      border-radius: 20px;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: all 0.4s ease;
      }

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);

        &::before {
          left: 100%;
          transition: all 0.8s ease;
        }
      }

      &:active {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

// 动画定义
@keyframes shine {
  0% {
    left: -100px;
    opacity: 0.5;
  }
  100% {
    left: 100%;
    opacity: 0.2;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

.el-link {
  margin-right: 12px;
  position: relative;
  transition: all 0.3s ease;
  font-weight: 500;

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 1px;
    background: currentColor;
    transition: width 0.3s ease;
    opacity: 0.7;
  }

  &:hover {
    transform: translateY(-2px);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &::after {
      width: 100%;
    }
  }
}

.el-link .el-icon--right.el-icon {
  vertical-align: text-bottom;
  transition: transform 0.3s ease;
}

.el-link:hover .el-icon--right.el-icon {
  transform: translateX(3px);
}
