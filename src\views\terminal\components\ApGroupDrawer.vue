<template>
  <el-drawer
    v-model="drawerVisible"
    :destroy-on-close="true"
    size="680px"
    :title="`${drawerProps.title || ''} ${t('device.apGroup')}`"
    @open="getApGroupRadioConfig"
    @close="onDrawerClose"
  >
    <el-form
      ref="ruleFormRef"
      label-width="120px"
      label-position="left"
      label-suffix=" :"
      :rules="rules"
      :disabled="drawerProps.isView || false"
      :model="drawerProps.row || {}"
      :hide-required-asterisk="drawerProps.isView || false"
    >
      <el-form-item :label="$t('device.apGroup')" label-width="160px">
        <el-select v-model="group_id" :placeholder="$t('common.pleaseSelect')" clearable>
          <el-option v-for="item in apGroupList" :key="item.group_id" :label="item.alias" :value="item.group_id" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('device.name')" label-width="160px" v-if="currentApGroup">
        <el-text v-if="!editName">{{ currentApGroup!.alias || "--" }}</el-text>
        <el-link v-show="!(drawerProps.isView || false)" :icon="Edit" v-if="!editName" @click="editName = true">
          {{ $t("project.edit") }}
        </el-link>
        <el-input v-model="currentApGroup!.alias" v-if="editName" style="width: 60% !important" clearable>
          <template #append>
            <el-link
              v-show="!(drawerProps.isView || false)"
              :icon="Check"
              v-if="editName"
              @click="handleSubmit()"
              :class="{ 'is-disabled': !hasNameChanged }"
              :style="{ cursor: hasNameChanged ? 'pointer' : 'not-allowed', opacity: hasNameChanged ? 1 : 0.5 }"
            >
              {{ $t("common.save") }}
            </el-link>
          </template>
        </el-input>
      </el-form-item>

      <el-divider></el-divider>
      <el-tabs type="card" v-model="activeName">
        <el-tab-pane :label="$t('device.networkSettings')" name="first">
          <el-form-item :label="$t('device.txpower')" label-position="left" label-width="160px" v-if="currentApGroup">
            <el-select v-if="currentRadio0?.txpower !== undefined" v-model="currentRadio0.txpower" clearable>
              <el-option
                v-for="percent in [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]"
                :key="percent"
                :label="`${percent}%`"
                :value="percent"
              />
            </el-select>
          </el-form-item>
          <el-tabs type="card" v-model="activeNetworkName" stretch v-if="showNetworkTabs">
            <el-tab-pane label="2.4G" name="first">
              <!-- 2.4GHz -->
              <el-card
                class="box-card"
                shadow="always"
                :style="{ marginTop: '10px', marginBottom: '10px' }"
                v-for="(radio0, index) in currentRadio0?.wifi || []"
                :key="index"
              >
                <el-header>
                  <el-form-item :label="$t('device.wifi24GHz')" label-position="left" label-width="160px">
                    <el-switch
                      v-model="radio0.disabled"
                      :active-text="$t('device.open')"
                      :inactive-text="$t('device.close')"
                      :active-value="0"
                      :inactive-value="1"
                      active-color="#13ce66"
                      inactive-color="#ff4949"
                    />
                  </el-form-item>
                </el-header>
                <el-main v-if="radio0.disabled === 0">
                  <el-form-item :label="$t('device.name')" label-position="left" label-width="160px">
                    <el-input v-model="radio0.ssid" clearable />
                  </el-form-item>
                  <el-form-item :label="$t('device.hidden')" label-position="left" label-width="160px">
                    <el-switch v-model="radio0.hidden" :active-value="1" :inactive-value="0" />
                  </el-form-item>
                  <el-form-item :label="$t('device.key')" label-position="left" v-if="encryptRadio0">
                    <el-input type="password" v-model="radio0.key" show-password clearable />
                  </el-form-item>
                </el-main>
              </el-card>
            </el-tab-pane>
            <el-tab-pane label="5G" name="second">
              <!-- 5G -->
              <el-card
                class="box-card"
                shadow="always"
                :style="{ marginTop: '10px', marginBottom: '10px' }"
                v-for="(radio1, index) in currentRadio1?.wifi || []"
                :key="index"
              >
                <el-header>
                  <el-form-item :label="$t('device.wifi5GHz')" label-position="left">
                    <el-switch
                      v-model="radio1.disabled"
                      active-color="#13ce66"
                      :active-value="0"
                      :inactive-value="1"
                      inactive-color="#ff4949"
                      :active-text="$t('device.open')"
                      :inactive-text="$t('device.close')"
                    />
                  </el-form-item>
                </el-header>
                <el-main v-if="radio1.disabled === 0">
                  <el-form-item :label="$t('device.name')" label-position="left" label-width="160px">
                    <el-input v-model="radio1.ssid" clearable />
                  </el-form-item>
                  <el-form-item :label="$t('device.hidden')" label-position="left" label-width="160px">
                    <el-switch v-model="radio1.hidden" :active-value="1" :inactive-value="0" />
                  </el-form-item>
                  <el-form-item :label="$t('device.key')" label-position="left" v-if="encryptRadio1">
                    <el-input type="password" v-model="radio1.key" show-password clearable />
                  </el-form-item>
                </el-main>
              </el-card>
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane>
        <el-tab-pane :label="t('device.systemSettings')" name="second">
          <el-form-item
            :label="t('device.ledConfiguration')"
            label-position="left"
            v-if="currentApGroup?.ledoff !== undefined"
            label-width="160px"
          >
            <el-select v-model="currentApGroup.ledoff" :placeholder="t('common.pleaseSelect')" clearable>
              <el-option :label="t('device.on')" :value="0"></el-option>
              <el-option :label="t('device.off')" :value="1"></el-option>
            </el-select>
          </el-form-item>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <template #footer>
      <el-button @click="drawerVisible = false">{{ $t("common.cancel") }}</el-button>
      <el-button v-show="!(drawerProps.isView || false)" type="primary" :disabled="!hasConfigChanged" @click="handleSubmit">
        {{ $t("common.confirm") }}
      </el-button>
    </template>
  </el-drawer>
</template>

<script setup lang="ts" name="ApGroupDrawer">
import { reactive, ref, computed, watch } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { useI18n } from "vue-i18n";
import { apGroupList, DeviceDataCmd, encryptRadio0, encryptRadio1 } from "@/api/interface/apGroupDrawer";
import { getDeviceConfigJwe } from "@/api/modules/project";
import { useUserStore } from "@/stores/modules/user";
import { Check, Edit } from "@element-plus/icons-vue";

const { t } = useI18n();

const activeName = ref<"first" | "second">("first"); // 底层tabs默认选中第一个
const activeNetworkName = ref<"first" | "second">("first"); // 底层tabs默认选中第一个
const group_id = ref<number>(0);
const originalName = ref(""); // 保存原始名称，用于比较名称是否有变化

// 计算属性：当前选中的AP组
const currentApGroup = computed(() => {
  if (!drawerProps.value || !drawerProps.value.row || !drawerProps.value.row.apGroup) return null;
  return drawerProps.value.row.apGroup.find(item => item.group_id === group_id.value);
});

// 计算属性：当前选中AP组的2.4G配置
const currentRadio0 = computed(() => {
  return currentApGroup.value?.radio0 || null;
});

// 计算属性：当前选中AP组的5G配置
const currentRadio1 = computed(() => {
  return currentApGroup.value?.radio1 || null;
});

// 计算属性：是否有2.4G WiFi配置
const hasRadio0Wifi = computed(() => {
  return currentRadio0.value?.wifi && Object.keys(currentRadio0.value.wifi).length > 0;
});

// 计算属性：是否有5G WiFi配置
const hasRadio1Wifi = computed(() => {
  return currentRadio1.value?.wifi && Object.keys(currentRadio1.value.wifi).length > 0;
});

// 计算属性：是否显示网络设置选项卡
const showNetworkTabs = computed(() => {
  return hasRadio0Wifi.value || hasRadio1Wifi.value;
});

const drawerVisible = ref(false);

const editName = ref(false);

// 保存原始数据的副本，用于比较是否有变化
const originalData = ref(null);

interface WifiConfig {
  hidden: number;
  disabled: number;
  ssid: string;
  key: string;
}

interface RadioConfig {
  wifi: WifiConfig[];
  txpower: number;
  channel: number;
}

interface DeviceData {
  private: number;
  group_id: number;
  station: number;
  alias: string;
  online: number;
  model: string;
  ipaddr: string;
  version: string;
  ledoff: number;
  mac: string;
  radio0: RadioConfig;
  radio1: RadioConfig;
}

const onDrawerClose = () => {
  editName.value = false;
  activeName.value = "first";
  activeNetworkName.value = "first";
  group_id.value = 0;
  originalData.value = null; // 关闭抽屉时清空原始数据
  originalName.value = ""; // 关闭抽屉时重置原始名称
};

interface DrawerProps {
  title: string;
  isView: boolean;
  row: Partial<DeviceData> & {
    apGroup?: any[];
    deviceId?: string;
    status?: number;
  };
  api?: (params: any) => Promise<any>;
  getTableList?: () => void;
}

const drawerProps = ref<DrawerProps>({
  isView: false,
  title: "",
  row: {},
  api: undefined,
  getTableList: undefined
});
// 接收父组件传过来的参数
const acceptParams = (params: DrawerProps) => {
  drawerProps.value = params;
  drawerVisible.value = true;
  originalData.value = null; // 重置原始数据，等待数据加载完成后再保存
};

// 提交数据（新增/编辑）
const ruleFormRef = ref<FormInstance>();
const handleSubmit = () => {
  ruleFormRef.value!.validate(async valid => {
    if (!valid) return;

    // 如果名称没有变化，则不允许提交
    if (!hasNameChanged.value && editName.value) {
      ElMessage.warning(t("common.noConfigChanges"));
      return;
    }

    try {
      if (!drawerProps.value || !drawerProps.value.api) {
        ElMessage.error(t("common.operationFailed"));
        return;
      }

      const params = {
        scope: {
          deviceId: drawerProps.value.row?.deviceId || "",
          row: drawerProps.value.row || {}
        },
        row: drawerProps.value.row
      };
      const response = await drawerProps.value.api(params);
      console.log("API 响应数据:", response);
      if (!response || response.code !== "200") {
        ElMessage.error({ message: response.msg });
        return;
      }
      ElMessage.success({ message: t("common.operationSuccess") });
      if (drawerProps.value.getTableList) {
        drawerProps.value.getTableList();
      }
      drawerVisible.value = false;
    } catch (error) {
      console.log(error);
    }
  });
};

const rules = reactive({
  deviceId: [{ required: false, message: t("device.deviceIdPlaceholder") }],
  deviceModel: [{ required: false, message: t("device.modelPlaceholder") }],
  deviceType: [{ required: false, message: t("device.typePlaceholder") }],
  mac: [{ required: false, message: t("device.macPlaceholder") }],
  ip: [{ required: false, message: t("device.ipPlaceholder") }]
});

const getApGroupRadioConfig = async () => {
  try {
    const params = {
      cmd: DeviceDataCmd.CONFIG,
      deviceId: drawerProps.value?.row?.deviceId || "",
      userId: useUserStore().userInfo.userId,
      data: {
        system: ["apGroup"]
      }
    };
    const response = await getDeviceConfigJwe(params);
    if (response?.data?.system?.apGroup) {
      // 更新 AP 组数据
      if (drawerProps.value && drawerProps.value.row) {
        drawerProps.value.row.apGroup = response.data.system.apGroup;
        // 如果没有选择组，默认选择第一个
        if (group_id.value === 0 && drawerProps.value.row.apGroup.length > 0) {
          group_id.value = drawerProps.value.row.apGroup[0].group_id;
        }
      }

      // 保存原始数据的深拷贝，用于后续比较变化
      if (drawerProps.value && drawerProps.value.row) {
        originalData.value = JSON.parse(JSON.stringify(drawerProps.value.row));

        // 如果已经选择了组，则保存原始名称
        if (group_id.value > 0 && drawerProps.value.row.apGroup) {
          const selectedGroup = drawerProps.value.row.apGroup.find(group => group.group_id === group_id.value);
          if (selectedGroup) {
            originalName.value = selectedGroup.alias || "";
            console.log("初始化时保存原始名称:", originalName.value);

            // 确保 currentApGroup 也被正确设置
            if (currentApGroup.value) {
              currentApGroup.value.alias = selectedGroup.alias || "";
            }
          }
        }
      }
    }
  } catch (error) {
    console.error("获取 AP 组配置失败:", error);
    ElMessage.error(t("common.operationFailed"));
  }
};

// 计算属性：检测名称是否有变化
const hasNameChanged = computed(() => {
  if (!currentApGroup.value || originalName.value === undefined || originalName.value === "") {
    return false;
  }

  // 检查名称是否发生变化
  const currentName = currentApGroup.value.alias || "";
  const changed = currentName !== originalName.value;

  // 打印调试信息，查看当前名称和原始名称
  console.log(`当前名称: "${currentName}", 原始名称: "${originalName.value}", 是否变化: ${changed}, 编辑模式: ${editName.value}`);

  // 只有在编辑模式下才检测名称是否变化
  return editName.value ? changed : false;
});

// 监听 currentApGroup 变化，保存原始名称
watch(
  () => currentApGroup.value,
  newVal => {
    if (newVal && editName.value === false) {
      // 当编辑模式切换为非编辑模式时，更新原始名称
      originalName.value = newVal.alias || "";
      console.log("更新原始名称:", originalName.value);
    }
  },
  { deep: true }
);

// 监听编辑模式变化
watch(
  () => editName.value,
  (newVal, oldVal) => {
    // 当从编辑模式切换到非编辑模式时，更新原始名称
    if (oldVal === true && newVal === false && currentApGroup.value) {
      originalName.value = currentApGroup.value.alias || "";
      console.log("编辑模式切换，更新原始名称:", originalName.value);
    }
  }
);

// 计算属性：检测配置是否有变化
const hasConfigChanged = computed(() => {
  // 只有当名称发生变化时，才启用保存按钮
  return hasNameChanged.value;
});

defineExpose({
  acceptParams
});
</script>
<style scoped lang="scss">
.device-img {
  width: 80px;
  height: 80px;
  object-fit: contain; /* 确保图片比例适配 */
  margin-bottom: 10px;
}

.sw-port-list {
  display: flex; /* 设置为横向排列 */
  flex-wrap: wrap; /* 如果元素过多，换行显示 */
  gap: 10px; /* 设置每个 item 之间的间距 */
  padding: 0; /* 去掉默认的 padding */
  margin: 0; /* 去掉默认的 margin */
}

.sw-port-tag {
  display: flex; /* 设置为 flex 容器 */
  flex-direction: column; /* 垂直排列图标和名称 */
  align-items: center; /* 图标和名称居中 */
  justify-content: center; /* 纵向居中对齐 */
  width: 50px; /* 固定宽度 */
  height: 65px; /* 固定高度 */
  box-sizing: border-box; /* 包括边框和内边距在尺寸内 */
  padding: 5px; /* 内边距调整为 10px */
  background-color: #f9f9f9; /* 默认背景色 */
  border: 1px solid transparent; /* 默认边框透明 */
  border-radius: 5px; /* 可选，增加圆角 */
  margin: 1px; /* 设置间距 */
  transition: all 0.3s ease; /* 添加过渡效果 */
}

.sw-port-list li {
  text-align: center; /* 文字居中 */
}

.sw-port-example {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-shrink: 0; /* 防止内容缩小导致换行 */
  margin-right: 15px; /* 给每个项增加右边距，确保之间有间隔 */
  white-space: nowrap; /* 确保文字不换行 */
}

.port-icon {
  height: 20px;
  width: 20px;
  margin-right: 8px; /* 图标与文字之间的间距 */
}

.port-text {
  white-space: nowrap; /* 确保文字不换行 */
  line-height: 20px; /* 保证文字与图标垂直居中 */
}

/* 特殊样式 */
.selected-port {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff; /* 设置边框宽度为 1px */
  border-radius: 5px; /* 可选，增加圆角效果 */
}
</style>
